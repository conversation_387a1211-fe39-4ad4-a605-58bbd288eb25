package adapters_test

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func TestMinimalRoundTripConversion(t *testing.T) {
	design := adapters.Design{ID: uuid.NewString()}
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)

	// Account for expected diffs:
	design.LastUpdatedDateTime = roundTripped.LastUpdatedDateTime
	design.WallTilePlacement = roundTripped.WallTilePlacement
	design.WallpaperPlacement = roundTripped.WallpaperPlacement
	nope := false
	design.IsShowerGlassVisible = &nope
	design.IsTubDoorVisible = &nope
	design.IsNichesVisible = &nope

	assert.Equal(t, design, *roundTripped)
}

func TestTrivialRoundTripConversion(t *testing.T) {
	lastUpdated := "2025-06-13T00:59:59Z"
	vanityWall := usecases.VanityWall
	none := usecases.NoWallTile
	nope := false
	design := adapters.Design{
		ID:                   uuid.NewString(),
		Tags:                 0,
		LastUpdatedDateTime:  &lastUpdated,
		WallpaperPlacement:   &vanityWall,
		WallTilePlacement:    &none,
		IsShowerGlassVisible: &nope,
		IsTubDoorVisible:     &nope,
		IsNichesVisible:      &nope,
	}
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)
	assert.Equal(t, design, *roundTripped)
}

func TestTypicalRoundTripConversion(t *testing.T) {
	lastUpdated := "2025-06-13T00:59:59Z"
	title := "Test Design"
	description := "This is a test design"
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	lighting := uuid.NewString()
	paint := uuid.NewString()
	shelves := uuid.NewString()
	showerSystem := uuid.NewString()
	showerWallTile := uuid.NewString()
	showerWallTilePattern := usecases.Herringbone
	tub := uuid.NewString()
	wallpaper := uuid.NewString()
	wallTile := uuid.NewString()
	wallTilePattern := usecases.Herringbone
	showerFloorTile := uuid.NewString()
	showerFloorTilePattern := usecases.Herringbone
	tubDoor := uuid.NewString()
	halfWall := usecases.HalfWall
	vanityWall := usecases.VanityWall
	nope := false
	design := adapters.Design{
		ID:                     uuid.NewString(),
		Tags:                   36,
		LastUpdatedDateTime:    &lastUpdated,
		Title:                  &title,
		Description:            &description,
		FloorTile:              &floorTile,
		Toilet:                 &toilet,
		Vanity:                 &vanity,
		Faucet:                 &faucet,
		Mirror:                 &mirror,
		Lighting:               &lighting,
		Paint:                  &paint,
		Shelves:                &shelves,
		ShowerFloorTile:        &showerFloorTile,
		ShowerFloorTilePattern: &showerFloorTilePattern,
		ShowerSystem:           &showerSystem,
		ShowerWallTile:         &showerWallTile,
		ShowerWallTilePattern:  &showerWallTilePattern,
		Tub:                    &tub,
		TubDoor:                &tubDoor,
		WallpaperPlacement:     &vanityWall,
		Wallpaper:              &wallpaper,
		WallTilePlacement:      &halfWall,
		WallTile:               &wallTile,
		WallTilePattern:        &wallTilePattern,
		IsShowerGlassVisible:   &nope,
		IsTubDoorVisible:       &nope,
		IsNichesVisible:        &nope,
	}
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)
	assert.Equal(t, design, *roundTripped)
}

func TestMaximalRoundTripConversion(t *testing.T) {
	lastUpdated := "2025-06-13T00:59:59Z"
	title := "Test Design"
	description := "This is a test design"
	floorTile := uuid.NewString()
	floorTilePattern := usecases.Herringbone
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	lighting := uuid.NewString()
	paint := uuid.NewString()
	shelves := uuid.NewString()
	showerSystem := uuid.NewString()
	showerWallTile := uuid.NewString()
	showerWallTilePattern := usecases.Herringbone
	showerShortWallTile := uuid.NewString()
	showerGlass := uuid.NewString()
	tub := uuid.NewString()
	tubFiller := uuid.NewString()
	wallpaper := uuid.NewString()
	wallTile := uuid.NewString()
	wallTilePattern := usecases.Herringbone
	showerFloorTile := uuid.NewString()
	showerFloorTilePattern := usecases.Herringbone
	nicheTile := uuid.NewString()
	halfWall := usecases.HalfWall
	vanityWall := usecases.VanityWall
	nope := false
	design := adapters.Design{
		ID:                     uuid.NewString(),
		Tags:                   40,
		LastUpdatedDateTime:    &lastUpdated,
		Title:                  &title,
		Description:            &description,
		FloorTile:              &floorTile,
		FloorTilePattern:       &floorTilePattern,
		Toilet:                 &toilet,
		Vanity:                 &vanity,
		Faucet:                 &faucet,
		Mirror:                 &mirror,
		Lighting:               &lighting,
		NicheTile:              &nicheTile,
		Paint:                  &paint,
		Shelves:                &shelves,
		ShowerFloorTile:        &showerFloorTile,
		ShowerFloorTilePattern: &showerFloorTilePattern,
		ShowerSystem:           &showerSystem,
		ShowerWallTile:         &showerWallTile,
		ShowerWallTilePattern:  &showerWallTilePattern,
		ShowerShortWallTile:    &showerShortWallTile,
		ShowerGlass:            &showerGlass,
		Tub:                    &tub,
		TubFiller:              &tubFiller,
		TubDoor:                &tub,
		WallpaperPlacement:     &vanityWall,
		Wallpaper:              &wallpaper,
		WallTilePlacement:      &halfWall,
		WallTile:               &wallTile,
		WallTilePattern:        &wallTilePattern,
		IsShowerGlassVisible:   &nope,
		IsTubDoorVisible:       &nope,
		IsNichesVisible:        &nope,
	}
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)
	assert.Equal(t, design, *roundTripped)
}
