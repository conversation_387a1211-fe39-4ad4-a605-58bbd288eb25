package controllers

import (
	"context"
	"log/slog"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type DesignWriteController struct {
	generator textGenerator
	maker     usecases.DesignCreater
	fetcher   usecases.DesignRetriever
	changer   usecases.DesignUpdater
	bulkAdder usecases.BulkDesignSaver
	logger    *slog.Logger
}

func NewDesignWriteController(tg textGenerator, maker usecases.DesignCreater, changer usecases.DesignUpdater, logger *slog.Logger) *DesignWriteController {
	if adapters.IsNil(tg) {
		panic("generator cannot be nil")
	}
	if adapters.IsNil(maker) {
		panic("maker cannot be nil")
	}
	if adapters.IsNil(changer) {
		panic("changer cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignWriteController{generator: tg, maker: maker, changer: changer, logger: logger}
}

func (h *DesignWriteController) SaveDesign(ctx context.Context, projectId entities.ProjectId, design adapters.Design, presenter upsertOutcomePresenter) {
	if adapters.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	// TODO: Generate title and description asynchronously to reduce latency.
	title, description, err := h.generator.GenerateDesignTitleAndDescription(ctx, design)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	design.Title = &title
	design.Description = &description
	if design.ID == "" {
		d, err := design.ToUsecaseDesign(projectId)
		if err != nil {
			presenter.PresentError(err)
			return
		}
		h.maker.CreateDesign(ctx, presenter, d)
	} else {
		d, err := design.ToUsecaseDesignDiff()
		if err != nil {
			presenter.PresentError(err)
			return
		}
		h.changer.UpdateDesign(ctx, presenter, d)
	}
}

func (h *DesignWriteController) ModifyDesign(ctx context.Context, design adapters.Design, presenter upsertOutcomePresenter) {
	if adapters.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	if design.ID == "" {
		h.logger.ErrorContext(ctx, "Attempt to modify design with no ID")
		presenter.PresentError(usecases.ErrInvalidPayload)
		return
	}
	d, err := design.ToUsecaseDesignDiff()
	if err != nil {
		presenter.PresentError(err)
		return
	}
	h.changer.UpdateDesign(ctx, presenter, d)
}

func (h *DesignWriteController) SaveAllDesignsForProject(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design, presenter upsertOutcomePresenter) {
	if adapters.IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	data := make([]usecases.Design, len(designs))
	for i, d := range designs {
		design, err := d.ToUsecaseDesign(projectId)
		if err != nil {
			presenter.PresentError(err)
			return
		}
		data[i] = design
	}
	h.bulkAdder.SaveDesigns(ctx, presenter, data)
}
