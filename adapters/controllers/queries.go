package controllers

import (
	"context"
	"fmt"
	"log/slog"
	"slices"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type DesignAccessController struct {
	db     storage
	logger *slog.Logger
}

func NewDesignAccessController(db storage, logger *slog.Logger) *DesignAccessController {
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignAccessController{db: db, logger: logger}
}

func (h *DesignAccessController) IdsOfProjectsWithDesigns(ctx context.Context) ([]entities.ProjectId, error) {
	return h.db.IdsOfProjectsWithDesigns(ctx)
}

func (h *DesignAccessController) designs(ctx context.Context, projectId entities.ProjectId) ([]adapters.Design, error) {
	h.logger.InfoContext(ctx, "Loading designs for project", slog.String("projectId", projectId.String()))
	designs, err := h.db.DesignsForProject(ctx, projectId)
	if err != nil {
		h.logger.ErrorContext(ctx, "Unable to fetch designs for project",
			slog.String("projectId", projectId.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("project %s not found: %w", projectId.String(), err)
	}
	if len(designs) == 0 {
		h.logger.InfoContext(ctx, "Project has no designs", slog.String("projectId", projectId.String()))
	}
	return designs, nil
}

func (h *DesignAccessController) FetchAllDesignsForProject(ctx context.Context, projectId entities.ProjectId, presenter designsPresenter) {
	designs, err := h.designs(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if len(designs) == 0 {
		designs = []adapters.Design{}
	}
	h.logger.InfoContext(ctx, "Serving designs", slog.Int("count", len(designs)))
	presenter.PresentData(ctx, designs)
}

func (h *DesignAccessController) FetchDesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId, presenter designsPresenter) {
	designs, errors, err := h.db.DesignsForMultipleProjects(ctx, projectIds)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if len(errors) > 0 {
		h.logger.ErrorContext(ctx, "Failed to fetch designs for some projects", slog.Int("count", len(errors)))
	}
	h.logger.InfoContext(ctx, "Serving designs for multiple projects", slog.Int("count", len(designs)))
	presenter.PresentMultipleResults(ctx, designs, errors)
}

func (h *DesignAccessController) FetchDesign(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID, presenter designsPresenter) {
	designs, err := h.designs(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if len(designs) == 0 {
		h.logger.ErrorContext(ctx, "Project has no designs", slog.String("projectId", projectId.String()))
		presenter.PresentError(fmt.Errorf("project %s has no designs", projectId))
		return
	}
	i := slices.IndexFunc(designs, func(d adapters.Design) bool {
		return d.ID == designId.String()
	})
	if i < 0 {
		h.logger.ErrorContext(ctx, "Design not found", slog.String("designId", designId.String()))
		presenter.PresentError(usecases.ErrNotFound)
		return
	}
	design := designs[i]
	h.logger.InfoContext(ctx, "Serving design", slog.String("designId", designId.String()))
	presenter.PresentData(ctx, design)
}
