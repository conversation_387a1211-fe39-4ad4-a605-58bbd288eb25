package controllers

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type storage interface {
	IdsOfProjectsWithDesigns(ctx context.Context) ([]entities.ProjectId, error)
	DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]adapters.Design, error)
	DesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId) (map[entities.ProjectId][]adapters.Design, map[entities.ProjectId]error, error)
	UpdateDesigns(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design) error
	MarkRenderOutdated(ctx context.Context, designId uuid.UUID) error
}

type textGenerator interface {
	GenerateDesignTitleAndDescription(ctx context.Context, design adapters.Design) (string, string, error)
}

type designsPresenter interface {
	PresentError(err error)
	PresentData(ctx context.Context, data any)
	PresentDesign(ctx context.Context, design usecases.Design)
	PresentDesigns(ctx context.Context, designs []usecases.Design)
	PresentMultipleResults(ctx context.Context, data map[entities.ProjectId][]adapters.Design, errors map[entities.ProjectId]error)
}

type upsertOutcomePresenter interface {
	PresentError(err error)
	ConveySuccess(status usecases.Status, projectId *entities.ProjectId, designId *uuid.UUID)
}
