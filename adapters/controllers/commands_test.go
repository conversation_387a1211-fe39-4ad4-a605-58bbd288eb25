package controllers_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"github.com/google/uuid"
	"github.com/openai/openai-go"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	return adapters.Design{
		ID:        uuid.NewString(),
		FloorTile: &floorTile,
		Toilet:    &toilet,
		Vanity:    &vanity,
		Faucet:    &faucet,
		Mirror:    &mirror,
	}
}

// TODO: move memStore to a separate package that can be shared with other tests.
type memStore struct {
	designs map[entities.ProjectId][]adapters.Design
}

func newMemStore() *memStore {
	m := memStore{
		designs: make(map[entities.ProjectId][]adapters.Design),
	}
	return &m
}

func (m *memStore) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]adapters.Design, error) {
	results, ok := m.designs[projectId]
	if !ok {
		return nil, fmt.Errorf("project %s not found", projectId)
	}
	return results, nil
}

func (m *memStore) DesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId) (map[entities.ProjectId][]adapters.Design, map[entities.ProjectId]error, error) {
	results := make(map[entities.ProjectId][]adapters.Design)
	errors := make(map[entities.ProjectId]error)
	for _, projectId := range projectIds {
		designs, err := m.DesignsForProject(ctx, projectId)
		if err != nil {
			errors[projectId] = err
			continue
		}
		results[projectId] = designs
	}
	return results, errors, nil
}

func (m *memStore) addDesignToProject(projectId entities.ProjectId, design adapters.Design) error {
	m.designs[projectId] = append(m.designs[projectId], design)
	return nil
}

func (m *memStore) UpdateDesigns(ctx context.Context, projectId entities.ProjectId, designs []adapters.Design) error {
	if _, ok := m.designs[projectId]; !ok {
		return fmt.Errorf("project %s not found", projectId)
	}
	m.designs[projectId] = designs
	return nil
}

func (m *memStore) MarkRenderOutdated(ctx context.Context, designId uuid.UUID) error {
	return nil
}

func (m *memStore) IdsOfProjectsWithDesigns(_ context.Context) ([]entities.ProjectId, error) {
	var projectIds []entities.ProjectId
	for projectId := range m.designs {
		projectIds = append(projectIds, projectId)
	}
	return projectIds, nil
}

const (
	projId             = "PRJ-FOOBAR"
	jsonSchemaFilename = "../../room-design.schema.json"
)

func setup(t *testing.T) (*controllers.DesignMutationController, *memStore, adapters.Design) {
	t.Helper()
	m := newMemStore()
	tg := gateways.NewOpenAI(openai.NewClient())
	handler := controllers.NewDesignMutationController(m, tg, nil)
	testDesign := genDesign()
	if err := m.addDesignToProject(projId, testDesign); err != nil {
		t.Fatal(err)
	}
	return handler, m, testDesign
}

func TestAddingDesign(t *testing.T) {
	handler, store, _ := setup(t)
	testDesign2 := genDesign()
	data, err := json.Marshal(testDesign2)
	if err != nil {
		t.Fatal(err)
	}
	req := httptest.NewRequest("POST", fmt.Sprintf("/projects/%s/designs", projId), bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.CreateDesign(t.Context(), projId, testDesign2, presenter)
	if status := recorder.Code; status != http.StatusCreated {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusCreated)
	}
	locationHeader := recorder.Header().Get("Location")
	if locationHeader == "" {
		t.Error("Location header not set")
	} else {
		expectedLocation := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign2.ID)
		if got, want := locationHeader, expectedLocation; got != want {
			t.Errorf("Location header mismatch: got %v want %v", got, want)
		}
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 2; got != want {
		t.Errorf("wrong number of results: got %d want %d", got, 2)
	}
}

func TestModifyingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	testDesign.Tags = 1
	vis := true
	testDesign.IsShowerGlassVisible = &vis
	patch := adapters.Design{
		ID:                   testDesign.ID,
		WallpaperPlacement:   &vanityWall,
		Tags:                 1,
		IsShowerGlassVisible: &vis,
	}
	data, err := json.Marshal(patch)
	if err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.ModifyDesign(t.Context(), projId, patch, presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := designs[0], testDesign; !reflect.DeepEqual(got, want) {
		t.Errorf("design mismatch: got %v want %v", got, want)
	}
}
func TestReplacingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("PUT", url, bytes.NewReader(data))
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.SaveDesign(t.Context(), projId, testDesign, presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := designs[0], testDesign; !reflect.DeepEqual(got, want) {
		t.Errorf("design mismatch: got %v want %v", got, want)
	}
}

func TestDeletingDesign(t *testing.T) {
	handler, store, testDesign := setup(t)
	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("DELETE", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignMutationOutcomePresenter(nil, recorder)
	handler.DeleteDesign(t.Context(), projId, uuid.MustParse(testDesign.ID), presenter)
	if status := recorder.Code; status != http.StatusNoContent {
		t.Errorf("presenter returned wrong status code: got %v want %v", status, http.StatusNoContent)
	}
	designs, err := store.DesignsForProject(context.Background(), projId)
	if err != nil {
		t.Fatal(err)
	}
	if got, want := len(designs), 0; got != want {
		t.Errorf("wrong number of designs: got %d want %d", got, want)
	}
}
