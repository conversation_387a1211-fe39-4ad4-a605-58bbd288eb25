package controllers

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

type DesignRetrievalController struct {
	retriever usecases.DesignRetriever
}

func NewDesignRetrievalController(retriever usecases.DesignRetriever) *DesignRetrievalController {
	return &DesignRetrievalController{retriever: retriever}
}

func (c *DesignRetrievalController) FetchDesign(ctx context.Context, designId uuid.UUID, presenter designsPresenter) {
	c.retriever.RetrieveDesign(ctx, presenter, designId)
}

func (h *DesignRetrievalController) FetchAllDesignsForProject(ctx context.Context, projectId entities.ProjectId, presenter designsPresenter) {
	h.retriever.RetrieveAllDesignsForProject(ctx, presenter, projectId)
}

func (h *DesignRetrievalController) FetchDesignsForMultipleProjects(ctx context.Context, projectIds []entities.ProjectId, presenter designsPresenter) {
}
