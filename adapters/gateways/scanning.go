package gateways

import (
	"errors"
	"fmt"

	"github.com/jackc/pgx/v5"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

// pgxScanner is an interface that covers both pgx.Row and pgx.Rows,
// allowing the scanDesign function to be used for both single-row and multi-row queries.
type pgxScanner interface {
	Scan(dest ...any) error
}

const readDesignBaseQuery = `SELECT
	rd.id, rd.project_id, rd.created_at, rd.updated_at,
	rd.status, rd.style, rd.color_scheme, rd.title, rd.description,
	dp.floor_tile, dp.floor_tile_pattern, dp.toilet, dp.vanity, dp.faucet,
	dp.mirror, dp.lighting, dp.paint, dp.shelving,
	dp.wall_tile_placement, dp.wall_tile, dp.wall_tile_pattern,
	dp.wallpaper_placement, dp.wallpaper,
	dp.shower_system, dp.shower_floor_tile, dp.shower_floor_tile_pattern,
	dp.shower_wall_tile, dp.shower_wall_tile_pattern, dp.shower_short_wall_tile,
	dp.shower_glass, dp.niche_tile, dp.tub, dp.tub_filler, dp.tub_door,
	rp.shower_glass_visible, rp.tub_door_visible, rp.niches_visible
FROM design.room_designs rd
INNER JOIN design.default_products dp ON rd.id = dp.room_design_id
INNER JOIN design.render_prefs rp ON rd.id = rp.room_design_id
`

// scanDesign scans a single row from a pgxScanner (like pgx.Row or pgx.Rows)
// into the constituent model structs and assembles them into a final Design entity.
func scanDesign(row pgxScanner) (*usecases.Design, error) {
	var rd RoomDesignModel
	var dp DefaultProductsModel
	var rp RenderPrefsModel

	err := row.Scan(
		&rd.ID, &rd.ProjectID, &rd.CreatedAt, &rd.UpdatedAt,
		&rd.Status, &rd.Style, &rd.ColorScheme, &rd.Title, &rd.Description,
		&dp.FloorTile, &dp.FloorTilePattern, &dp.Toilet, &dp.Vanity, &dp.Faucet,
		&dp.Mirror, &dp.Lighting, &dp.Paint, &dp.Shelving,
		&dp.WallTilePlacement, &dp.WallTile, &dp.WallTilePattern,
		&dp.WallpaperPlacement, &dp.Wallpaper,
		&dp.ShowerSystem, &dp.ShowerFloorTile, &dp.ShowerFloorTilePattern,
		&dp.ShowerWallTile, &dp.ShowerWallTilePattern, &dp.ShowerShortWallTile,
		&dp.ShowerGlass, &dp.NicheTile, &dp.Tub, &dp.TubFiller, &dp.TubDoor,
		&rp.ShowerGlassVisible, &rp.TubDoorVisible, &rp.NichesVisible,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("design not found")
		}
		return nil, fmt.Errorf("failed to scan design row: %w", err)
	}

	return newDesignFromModels(&rd, &dp, &rp), nil
}

// newDesignFromModels assembles the final domain Design entity from the scanned models
func newDesignFromModels(rd *RoomDesignModel, dp *DefaultProductsModel, rp *RenderPrefsModel) *usecases.Design {
	style := usecases.Style(rd.Style.String)
	colorScheme := usecases.ColorScheme(rd.ColorScheme.String)
	return &usecases.Design{
		ID:                 rd.ID,
		ProjectID:          entities.ProjectId(rd.ProjectID),
		Created:            rd.CreatedAt,
		LastUpdated:        rd.UpdatedAt,
		Status:             usecases.DesignStatus(rd.Status.String),
		WallTilePlacement:  dp.WallTilePlacement,
		WallpaperPlacement: dp.WallpaperPlacement,
		DesignOptions: usecases.DesignOptions{
			Style:                  &style,
			ColorScheme:            &colorScheme,
			Title:                  rd.Title,
			Description:            rd.Description,
			FloorTile:              dp.FloorTile,
			FloorTilePattern:       dp.FloorTilePattern,
			Toilet:                 dp.Toilet,
			Vanity:                 dp.Vanity,
			Faucet:                 dp.Faucet,
			Mirror:                 dp.Mirror,
			Lighting:               dp.Lighting,
			Paint:                  dp.Paint,
			Shelving:               dp.Shelving,
			WallTile:               dp.WallTile,
			WallTilePattern:        dp.WallTilePattern,
			Wallpaper:              dp.Wallpaper,
			ShowerSystem:           dp.ShowerSystem,
			ShowerFloorTile:        dp.ShowerFloorTile,
			ShowerFloorTilePattern: dp.ShowerFloorTilePattern,
			ShowerWallTile:         dp.ShowerWallTile,
			ShowerWallTilePattern:  dp.ShowerWallTilePattern,
			ShowerShortWallTile:    dp.ShowerShortWallTile,
			ShowerGlass:            dp.ShowerGlass,
			NicheTile:              dp.NicheTile,
			Tub:                    dp.Tub,
			TubFiller:              dp.TubFiller,
			TubDoor:                dp.TubDoor,
		},
		ShowerGlassVisible: rp.ShowerGlassVisible,
		TubDoorVisible:     rp.TubDoorVisible,
		NichesVisible:      rp.NichesVisible,
	}
}
