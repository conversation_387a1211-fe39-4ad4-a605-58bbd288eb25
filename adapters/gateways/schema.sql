SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: design; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA design;


--
-- Name: color_scheme_enum; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.color_scheme_enum AS ENUM (
    'Neutral',
    'Bold'
);


--
-- Name: rendition_status_enum; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.rendition_status_enum AS ENUM (
    'Pending',
    'Started',
    'Completed',
    'Archived',
    'Outdated'
);


--
-- Name: style_enum; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.style_enum AS ENUM (
    'Traditional',
    'Transitional',
    'Mid-century',
    'Modern'
);


--
-- Name: tile_pattern_enum; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.tile_pattern_enum AS ENUM (
    'Vertical',
    'Horizontal',
    'HalfOffset',
    'ThirdOffset',
    'Herringbone'
);


--
-- Name: maintain_updated_at_column(); Type: FUNCTION; Schema: design; Owner: -
--

CREATE FUNCTION design.maintain_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$ BEGIN NEW.updated_at = NOW();
RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: default_products; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.default_products (
    room_design_id uuid NOT NULL,
    floor_tile uuid NOT NULL,
    floor_tile_pattern design.tile_pattern_enum NOT NULL,
    toilet uuid NOT NULL,
    vanity uuid NOT NULL,
    faucet uuid NOT NULL,
    mirror uuid NOT NULL,
    lighting uuid NOT NULL,
    paint uuid NOT NULL,
    shelving uuid,
    wall_tile_placement text NOT NULL,
    wall_tile uuid,
    wall_tile_pattern design.tile_pattern_enum,
    wallpaper_placement text NOT NULL,
    wallpaper uuid,
    shower_system uuid,
    shower_floor_tile uuid,
    shower_floor_tile_pattern design.tile_pattern_enum,
    shower_wall_tile uuid,
    shower_wall_tile_pattern design.tile_pattern_enum,
    shower_short_wall_tile uuid,
    shower_glass uuid,
    niche_tile uuid,
    tub uuid,
    tub_filler uuid,
    tub_door uuid
);


--
-- Name: render_prefs; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.render_prefs (
    room_design_id uuid NOT NULL,
    shower_glass_visible boolean NOT NULL,
    tub_door_visible boolean NOT NULL,
    niches_visible boolean NOT NULL
);


--
-- Name: renditions; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.renditions (
    id uuid NOT NULL,
    room_design_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    status design.rendition_status_enum NOT NULL,
    url text,
    CONSTRAINT chk_rendition_url CHECK (((status <> 'Completed'::design.rendition_status_enum) OR (url IS NOT NULL)))
);


--
-- Name: retail_info; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.retail_info (
    room_design_id uuid NOT NULL,
    total_price_cents integer,
    lead_time_days integer,
    sku_count integer
);


--
-- Name: room_designs; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.room_designs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    project_id text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    status text,
    title text,
    description text,
    color_scheme design.color_scheme_enum,
    style design.style_enum
);


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    version character varying NOT NULL
);


--
-- Name: default_products default_products_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.default_products
    ADD CONSTRAINT default_products_pkey PRIMARY KEY (room_design_id);


--
-- Name: render_prefs render_prefs_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.render_prefs
    ADD CONSTRAINT render_prefs_pkey PRIMARY KEY (room_design_id);


--
-- Name: renditions renditions_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.renditions
    ADD CONSTRAINT renditions_pkey PRIMARY KEY (id);


--
-- Name: retail_info retail_info_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.retail_info
    ADD CONSTRAINT retail_info_pkey PRIMARY KEY (room_design_id);


--
-- Name: room_designs room_designs_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.room_designs
    ADD CONSTRAINT room_designs_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: room_designs refresh_designs_updated_at; Type: TRIGGER; Schema: design; Owner: -
--

CREATE TRIGGER refresh_designs_updated_at BEFORE UPDATE ON design.room_designs FOR EACH ROW EXECUTE FUNCTION design.maintain_updated_at_column();


--
-- Name: default_products default_products_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.default_products
    ADD CONSTRAINT default_products_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: render_prefs render_prefs_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.render_prefs
    ADD CONSTRAINT render_prefs_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: renditions renditions_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.renditions
    ADD CONSTRAINT renditions_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: retail_info retail_info_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.retail_info
    ADD CONSTRAINT retail_info_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--


--
-- Dbmate schema migrations
--

INSERT INTO public.schema_migrations (version) VALUES
    ('20250618235404'),
    ('20250619143144');
