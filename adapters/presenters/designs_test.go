package presenters_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/kodeart/go-problem/v2"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	return adapters.Design{
		ID:        uuid.NewString(),
		FloorTile: &floorTile,
		Toilet:    &toilet,
		Vanity:    &vanity,
		Faucet:    &faucet,
		Mirror:    &mirror,
	}
}

func TestFetchingDesigns(t *testing.T) {
	testDesigns := []adapters.Design{genDesign(), genDesign(), genDesign()}
	data, err := json.Marshal(testDesigns)
	if err != nil {
		t.Fatal(err)
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentData(context.Background(), testDesigns)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
}

func TestFetchingSingleDesign(t *testing.T) {
	testDesign := genDesign()
	data, err := json.Marshal(testDesign)
	if err != nil {
		t.Fatal(err)
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentData(context.Background(), testDesign)
	if status := recorder.Code; status != http.StatusOK {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
}

func TestFetchingMixedResults(t *testing.T) {
	testDesigns1 := []adapters.Design{genDesign(), genDesign()}
	designs := map[entities.ProjectId][]adapters.Design{
		"PRJ-FOOBAR": testDesigns1,
	}
	errors := map[entities.ProjectId]error{
		"PRJ-FOOBAR2": usecases.ErrNotFound,
	}
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)
	presenter.PresentMultipleResults(context.Background(), designs, errors)
	if status := recorder.Code; status != http.StatusMultiStatus {
		t.Errorf("handler returned wrong status code: got %v want %v", status, http.StatusMultiStatus)
	}
	results := make(map[entities.ProjectId]presenters.MultiProjectOutputItem)
	for projectId, designs := range designs {
		results[projectId] = presenters.MultiProjectOutputItem{
			Status: http.StatusOK,
			Data:   designs,
		}
	}
	for projectId, err := range errors {
		results[projectId] = presenters.MultiProjectOutputItem{
			Status: http.StatusNotFound,
			Error: &problem.Problem{
				Title:    fmt.Sprintf("Could not fetch designs for project %s", projectId),
				Status:   http.StatusNotFound,
				Detail:   err.Error(),
				Instance: fmt.Sprintf("/projects/%s/designs", projectId),
			},
		}
	}
	data, err := json.Marshal(results)
	if err != nil {
		t.Fatal(err)
	}
	//(*jsontext.Value)(&data).Indent()
	expected := string(data)
	if body := recorder.Body.String(); body != expected {
		t.Errorf("handler returned unexpected body:\ngot: %v\nwant: %v", body, expected)
	}
}
