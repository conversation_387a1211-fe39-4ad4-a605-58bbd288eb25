package main

import (
	"context"
	"log"
	"log/slog"
	"net/http"
	"os"

	"github.com/alexflint/go-arg"
	"github.com/google/uuid"
	"github.com/openai/openai-go"
	"github.com/santhosh-tekuri/jsonschema/v6"

	"gitlab.com/arc-studio-ai/services/room-design-service/adapters"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design-service/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
	"gitlab.com/arc-studio-ai/services/room-design-service/frameworks/db"
	"gitlab.com/arc-studio-ai/services/room-design-service/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design-service/usecases"
)

const jsonSchemaFilename = "room-design.schema.json"

type Command string

const (
	CmdSrv Command = "srv"
	CmdRt  Command = "roundtrip"
)

type args struct {
	PgHost     string `arg:"env" default:"alpha-database.cin8ix3jru5g.us-west-2.rds.amazonaws.com"`
	PgDatabase string `arg:"env" default:"alpha-prototype"`
	Cmd        string `arg:"positional" default:"srv"`
}

func main() {
	ctx := context.Background()
	logger := setupLogger()
	schema := controllers.Schema(jsonSchemaFilename)

	var args args
	arg.MustParse(&args)

	db := setupDb(ctx, logger, args, os.Getenv("PGPASSWORD"))
	defer db.Close()

	textGenerator := gateways.NewOpenAI(openai.NewClient())
	cmdController := controllers.NewDesignMutationController(db, textGenerator, logger)
	queryController := controllers.NewDesignAccessController(db, logger)

	switch Command(args.Cmd) {
	case CmdSrv:
		startSrv(logger, schema, queryController, cmdController)
	case CmdRt:
		roundTrip(ctx, logger, queryController, cmdController)
	default:
		log.Fatalf("Unknown command: %s", args.Cmd)
	}
}

func setupLogger() *slog.Logger {
	removeTimeAttr := func(groups []string, a slog.Attr) slog.Attr {
		if a.Key == slog.TimeKey {
			return slog.Attr{}
		}
		return a
	}
	var programLevel = new(slog.LevelVar) // Info by default
	logHandler := slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{
		Level: programLevel, ReplaceAttr: removeTimeAttr,
	})
	logger := slog.New(logHandler)
	slog.SetDefault(logger) // Set the global logger, since this is main.
	return logger
}

func setupDb(ctx context.Context, logger *slog.Logger, args args, pgPwd string) *gateways.Postgres {
	db := db.NewPostgres(ctx, args.PgHost, args.PgDatabase, pgPwd, logger)
	count, err := db.CountProjectsWithDesigns(ctx)
	if err != nil {
		db.Close()
		log.Fatal(err)
	}
	log.Printf("DB has %d projects with designs.\n", count)
	return db
}

func startSrv(logger *slog.Logger, schema *jsonschema.Schema, queryController *controllers.DesignAccessController, cmdController *controllers.DesignMutationController) {
	writeHandler := web.NewDesignMutationHandler(logger, schema, cmdController)
	readHandler := web.NewHttpGetReqHandler(logger, queryController)
	web.RegisterHandlers(readHandler, writeHandler)

	log.Println("Server listening on :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatal(err)
	}
}

type rewriter struct {
	logger        *slog.Logger
	cmdController *controllers.DesignMutationController
}

func (r *rewriter) PresentError(err error) {
	r.logger.Error("Error fetching designs", slog.String("error", err.Error()))
}

func (r *rewriter) ConveySuccess(status usecases.Status, projectId *entities.ProjectId, designId *uuid.UUID) {
	r.logger.Debug("Successfully round-tripped designs for project:", slog.String("projectId", projectId.String()))
}

func (r *rewriter) PresentData(ctx context.Context, data any)                     {}
func (r *rewriter) PresentDesign(ctx context.Context, design usecases.Design)     {}
func (r *rewriter) PresentDesigns(ctx context.Context, designs []usecases.Design) {}

func (r *rewriter) PresentMultipleResults(ctx context.Context, data map[entities.ProjectId][]adapters.Design, errors map[entities.ProjectId]error) {
	for projectId, designs := range data {
		for i, design := range designs {
			designs[i] = presenters.UsePascalCaseTilePatterns(design)
		}
		r.cmdController.SaveAllDesignsForProject(ctx, projectId, designs, r)
	}
	for projectId, err := range errors {
		r.logger.ErrorContext(ctx, "Error loading designs for project:", slog.String("projectId", projectId.String()), slog.String("error", err.Error()))
	}
}

func roundTrip(ctx context.Context, logger *slog.Logger, queryController *controllers.DesignAccessController, cmdController *controllers.DesignMutationController) {
	projectIds, err := queryController.IdsOfProjectsWithDesigns(ctx)
	if err != nil {
		log.Fatal(err)
	}
	rewriter := &rewriter{logger: logger, cmdController: cmdController}
	queryController.FetchDesignsForMultipleProjects(ctx, projectIds, rewriter)
}
