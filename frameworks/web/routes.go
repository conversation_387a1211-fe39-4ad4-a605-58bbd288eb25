package web

import (
	"net/http"
)

func handleOptions(w http.ResponseWriter, _ *http.Request) {
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", "*")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PATCH, DELETE, PUT")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Content-Type")
	w.<PERSON>rite<PERSON>eader(http.StatusNoContent)
}

type ReadHandler interface {
	HandleGetSpecifiedDesign(w http.ResponseWriter, r *http.Request)
	HandleGetAllDesigns(w http.ResponseWriter, r *http.Request)
	HandleGetAllDesignsForMultipleProjects(w http.ResponseWriter, r *http.Request)
}

type WriteHandler interface {
	HandlePost(w http.ResponseWriter, r *http.Request)
	HandlePut(w http.ResponseWriter, r *http.Request)
	HandlePatch(w http.ResponseWriter, r *http.Request)
	HandleDelete(w http.ResponseWriter, r *http.Request)
	HandlePutAllDesignsForProject(w http.ResponseWriter, r *http.Request)
}

func RegisterHandlers(readHandler ReadHandler, writeHandler WriteHandler) {
	http.HandleFunc("OPTIONS /projects/{projectId}/designs/", handleOptions)
	http.HandleFunc("GET /projects/{projectId}/designs/{designId}", readHandler.HandleGetSpecifiedDesign)
	http.HandleFunc("GET /projects/{projectId}/designs/", readHandler.HandleGetAllDesigns)
	http.HandleFunc("GET /projects/{projectId}/designs", readHandler.HandleGetAllDesigns)
	http.HandleFunc("GET /projects/designs", readHandler.HandleGetAllDesignsForMultipleProjects)
	http.HandleFunc("POST /projects/{projectId}/designs/", writeHandler.HandlePost)
	http.HandleFunc("POST /projects/{projectId}/designs", writeHandler.HandlePost)
	http.HandleFunc("PUT /projects/{projectId}/designs/{designId}", writeHandler.HandlePut)
	http.HandleFunc("PUT /projects/{projectId}/designs/", writeHandler.HandlePutAllDesignsForProject)
	http.HandleFunc("PUT /projects/{projectId}/designs", writeHandler.HandlePutAllDesignsForProject)
	http.HandleFunc("PATCH /projects/{projectId}/designs/{designId}", writeHandler.HandlePatch)
	http.HandleFunc("DELETE /projects/{projectId}/designs/{designId}", writeHandler.HandleDelete)
	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
}
