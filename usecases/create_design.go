package usecases

import (
	"context"
)

type DesignCreater struct {
	designRepo designRepository
}

func (dc *DesignCreater) CreateDesign(ctx context.Context, presenter updateOutcomePresenter, design Design) {
	var err error
	if design.ID, err = dc.designRepo.UpsertDesign(ctx, design); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccess(Created, &design.ProjectID, &design.ID)
}
