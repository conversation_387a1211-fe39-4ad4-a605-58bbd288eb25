package usecases

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
)

type DesignRetriever struct {
	designRepo designRepositoryReplica
}

type designPresenter interface {
	PresentError(err error)
	PresentDesign(ctx context.Context, design Design)
	PresentDesigns(ctx context.Context, designs []Design)
}

func (dr *DesignRetriever) RetrieveDesign(ctx context.Context, presenter designPresenter, designId uuid.UUID) {
	design, err := dr.designRepo.ReadDesign(ctx, designId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesign(ctx, design)
}

func (dr *DesignRetriever) RetrieveAllDesignsForProject(ctx context.Context, presenter designPresenter, projectId entities.ProjectId) {
	designs, err := dr.designRepo.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesigns(ctx, designs)
}
