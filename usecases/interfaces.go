package usecases

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design-service/entities"
)

type designRepositoryReplica interface {
	ReadDesign(ctx context.Context, designId uuid.UUID) (Design, error)
	DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]Design, error)
}
type designRepository interface {
	designRepositoryReplica
	UpsertDesign(ctx context.Context, design Design) (uuid.UUID, error)
}

type updateOutcomePresenter interface {
	PresentError(err error)
	ConveySuccess(status Status, projectId *entities.ProjectId, designId *uuid.UUID)
}
