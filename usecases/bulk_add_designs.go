package usecases

import (
	"context"
	"log/slog"

	"github.com/google/uuid"
)

type BulkDesignSaver struct {
	logger     *slog.Logger
	designRepo designRepository
}

func NewBulkDesignSaver(designRepo designRepository, logger *slog.Logger) *BulkDesignSaver {
	if logger == nil {
		logger = slog.Default()
	}
	return &BulkDesignSaver{logger: logger, designRepo: designRepo}
}

func (bdr *BulkDesignSaver) SaveDesigns(ctx context.Context, presenter updateOutcomePresenter, designs []Design) {
	errors := []error{}
	for _, design := range designs {
		if design.ID == uuid.Nil {
			design.ID = uuid.New()
			design.Status = Preview
		}
		if _, err := bdr.designRepo.UpsertDesign(ctx, design); err != nil {
			bdr.logger.ErrorContext(ctx, "Failed to save design", slog.String("error", err.<PERSON><PERSON>r()))
			errors = append(errors, err)
		}
	}
	if len(errors) > 0 {
		presenter.PresentError(ErrInvalidPayload)
		return
	}
}
