package usecases

import (
	"context"
	"log"

	"github.com/google/uuid"
)

type DesignUpdater struct {
	designRepo designRepository
}

func (du *DesignUpdater) UpdateDesign(ctx context.Context, presenter updateOutcomePresenter, diff DesignDiff) {
	if diff.ID == uuid.Nil {
		log.Println("Design ID is nil when updating design.")
		presenter.PresentError(ErrInvalidPayload)
		return
	}
	design, err := du.designRepo.ReadDesign(ctx, diff.ID)
	if err != nil {
		log.Printf("Failed to read design when updating design: %v", err)
		presenter.PresentError(ErrNotFound)
		return
	}
	design = MergeDesigns(design, diff)
	if _, err := du.designRepo.UpsertDesign(ctx, design); err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.ConveySuccess(Updated, nil, nil)
}
